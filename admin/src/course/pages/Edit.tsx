import { Edit, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  Select,
  Upload,
  Button,
  message,
} from "antd";
import React from "react";
import { entityKey } from "@/course";
import { useNavigation } from "@refinedev/core";
import type { UploadProps } from "antd";
import { API_BASE_URL } from "@/common/env";
import { FaUpload } from "react-icons/fa";

const { TextArea } = Input;

const CourseEdit: React.FC = () => {
  const { formProps, saveButtonProps } = useForm({
    resource: entityKey,
  });

  const { goBack } = useNavigation();

  // 课程封面上传配置
  const coverUploadProps: UploadProps = {
    name: 'file',
    action: `${API_BASE_URL}/files/upload_to_minio`,
    data: {
      path: 'courses/covers/', // 指定存储路径
    },
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 设置课程封面URL到表单
        formProps.form?.setFieldsValue({
          cover_image: info.file.response.data.url,
        });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  // 海报图片上传配置
  const posterUploadProps: UploadProps = {
    name: 'file',
    action: `${API_BASE_URL}/files/upload_to_minio`,
    data: {
      path: 'courses/posters/', // 指定存储路径
    },
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 设置海报图片URL到表单
        formProps.form?.setFieldsValue({
          poster_url: info.file.response.data.url,
        });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <Edit
      saveButtonProps={saveButtonProps}
      headerButtons={[
        <button key="cancel" onClick={() => goBack()} className="ant-btn">
          取消
        </button>,
      ]}
    >
      <Form<any> {...formProps} layout="vertical">
        <Form.Item
          label="课程标题"
          name="title"
          rules={[{ required: true, message: "请输入课程标题" }]}
        >
          <Input placeholder="请输入课程标题" />
        </Form.Item>

        <Form.Item
          label="课程描述"
          name="description"
          rules={[{ required: true, message: "请输入课程描述" }]}
        >
          <TextArea 
            rows={4} 
            placeholder="请输入课程描述" 
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item
          label="导师"
          name="instructor"
        >
          <Input placeholder="请输入导师名称" />
        </Form.Item>

        <Form.Item
          label="课程封面"
          name="cover_image"
        >
          <Input hidden />
          <Upload
            {...coverUploadProps}
            showUploadList={false}
          >
            <Button icon={<FaUpload />}>上传课程封面</Button>
          </Upload>
          {formProps.initialValues?.cover_image && (
            <div className="mt-2">
              <img
                src={formProps.initialValues.cover_image}
                alt="课程封面预览"
                style={{ width: 200, height: 120, borderRadius: '8px', objectFit: 'cover' }}
              />
            </div>
          )}
        </Form.Item>

        <Form.Item
          label="海报图片"
          name="poster_url"
        >
          <Input hidden />
          <Upload
            {...posterUploadProps}
            showUploadList={false}
          >
            <Button icon={<FaUpload />}>上传海报图片</Button>
          </Upload>
          {formProps.initialValues?.poster_url && (
            <div className="mt-2">
              <img
                src={formProps.initialValues.poster_url}
                alt="海报图片预览"
                style={{ width: 200, height: 120, borderRadius: '8px', objectFit: 'cover' }}
              />
            </div>
          )}
        </Form.Item>

        <Form.Item
          label="标签"
          name="tags"
        >
          <Select
            mode="tags"
            placeholder="请输入标签，按回车添加"
            style={{ width: '100%' }}
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Form>
    </Edit>
  );
};

export default CourseEdit;
