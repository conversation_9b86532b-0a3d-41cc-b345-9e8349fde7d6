import React, { useState, useEffect } from "react";
import { Upload, Button, message } from "antd";
import type { UploadProps } from "antd";
import { API_BASE_URL } from "@/common/env";
import { FaUpload } from "react-icons/fa";

interface ImageUploadProps {
  value?: string;
  onChange?: (url: string) => void;
  uploadPath: string;
  buttonText: string;
  previewWidth?: number;
  previewHeight?: number;
  showPreview?: boolean;
  maxSize?: number; // 最大文件大小，单位MB
  acceptedTypes?: string[]; // 允许的文件类型
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  uploadPath,
  buttonText,
  previewWidth = 200,
  previewHeight = 120,
  showPreview = true,
  maxSize = 5, // 默认最大5MB
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'], // 默认支持的图片类型
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(value);

  // 当外部value变化时，更新预览URL
  useEffect(() => {
    setPreviewUrl(value);
  }, [value]);

  // 文件上传前的验证
  const beforeUpload = (file: File) => {
    // 检查文件类型
    const isValidType = acceptedTypes.includes(file.type);
    if (!isValidType) {
      const supportedFormats = acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ');
      message.error(`只支持 ${supportedFormats} 格式的图片文件！`);
      return false;
    }

    // 检查文件大小
    const isValidSize = file.size / 1024 / 1024 < maxSize;
    if (!isValidSize) {
      message.error(`图片大小不能超过 ${maxSize}MB！`);
      return false;
    }

    return true;
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: `${API_BASE_URL}/files/upload_to_minio`,
    data: {
      path: uploadPath,
    },
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    accept: acceptedTypes.join(','), // 限制文件选择器只显示支持的文件类型
    beforeUpload: beforeUpload, // 上传前验证
    onChange(info) {
      if (info.file.status === 'uploading') {
        // 可以在这里添加上传进度提示
        return;
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        const uploadedUrl = info.file.response.data.url;
        // 立即更新预览URL
        setPreviewUrl(uploadedUrl);
        // 调用外部传入的onChange回调
        onChange?.(uploadedUrl);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <div>
      <Upload
        {...uploadProps}
        showUploadList={false}
      >
        <Button icon={<FaUpload />}>{buttonText}</Button>
      </Upload>
      {showPreview && previewUrl && (
        <div className="mt-2">
          <img
            src={previewUrl}
            alt="图片预览"
            style={{
              width: previewWidth,
              height: previewHeight,
              borderRadius: '8px',
              objectFit: 'cover'
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
