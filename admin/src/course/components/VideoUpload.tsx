import React, { useState, useEffect } from "react";
import { Upload, Button, message, Progress } from "antd";
import type { UploadProps } from "antd";
import { API_BASE_URL } from "@/common/env";
import { FaVideo, FaPlay } from "react-icons/fa";

interface VideoUploadProps {
  value?: string;
  onChange?: (url: string, duration?: number) => void;
  uploadPath: string;
  buttonText: string;
  maxSize?: number; // 最大文件大小，单位MB
  acceptedTypes?: string[]; // 允许的文件类型
  showPreview?: boolean;
}

const VideoUpload: React.FC<VideoUploadProps> = ({
  value,
  onChange,
  uploadPath,
  buttonText,
  maxSize = 100, // 默认最大100MB
  acceptedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'], // 默认支持的视频类型
  showPreview = true,
}) => {
  const [videoUrl, setVideoUrl] = useState<string | undefined>(value);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // 当外部value变化时，更新视频URL
  useEffect(() => {
    setVideoUrl(value);
  }, [value]);

  // 获取视频时长
  const getVideoDuration = (file: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';
      
      video.onloadedmetadata = () => {
        window.URL.revokeObjectURL(video.src);
        const duration = Math.round(video.duration);
        resolve(duration);
      };
      
      video.onerror = () => {
        reject(new Error('无法读取视频信息'));
      };
      
      video.src = URL.createObjectURL(file);
    });
  };

  // 文件上传前的验证
  const beforeUpload = async (file: File) => {
    // 检查文件类型
    const isValidType = acceptedTypes.includes(file.type);
    if (!isValidType) {
      const supportedFormats = acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ');
      message.error(`只支持 ${supportedFormats} 格式的视频文件！`);
      return false;
    }

    // 检查文件大小
    const isValidSize = file.size / 1024 / 1024 < maxSize;
    if (!isValidSize) {
      message.error(`视频大小不能超过 ${maxSize}MB！`);
      return false;
    }

    try {
      // 获取视频时长
      const duration = await getVideoDuration(file);
      // 将时长存储在文件对象上，供后续使用
      (file as any).videoDuration = duration;
    } catch (error) {
      console.warn('获取视频时长失败:', error);
      // 不阻止上传，但会提示用户手动输入时长
    }

    return true;
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: `${API_BASE_URL}/files/upload_to_minio`,
    data: {
      path: uploadPath,
    },
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    accept: acceptedTypes.join(','), // 限制文件选择器只显示支持的文件类型
    beforeUpload: beforeUpload, // 上传前验证
    onChange(info) {
      if (info.file.status === 'uploading') {
        setUploading(true);
        setUploadProgress(info.file.percent || 0);
        return;
      }
      
      if (info.file.status === 'done') {
        setUploading(false);
        setUploadProgress(0);
        message.success(`${info.file.name} 上传成功`);
        const uploadedUrl = info.file.response.data.url;
        const duration = (info.file as any).videoDuration;
        
        // 立即更新视频URL
        setVideoUrl(uploadedUrl);
        // 调用外部传入的onChange回调，传递URL和时长
        onChange?.(uploadedUrl, duration);
      } else if (info.file.status === 'error') {
        setUploading(false);
        setUploadProgress(0);
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <div>
      <Upload
        {...uploadProps}
        showUploadList={false}
      >
        <Button 
          icon={<FaVideo />} 
          loading={uploading}
          disabled={uploading}
        >
          {uploading ? '上传中...' : buttonText}
        </Button>
      </Upload>
      
      {uploading && (
        <div style={{ marginTop: 8 }}>
          <Progress percent={Math.round(uploadProgress)} size="small" />
        </div>
      )}
      
      {showPreview && videoUrl && !uploading && (
        <div style={{ marginTop: 12 }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 8,
            padding: 8,
            border: '1px solid #d9d9d9',
            borderRadius: 6,
            backgroundColor: '#fafafa'
          }}>
            <FaPlay style={{ color: '#1890ff' }} />
            <span style={{ fontSize: 12, color: '#666' }}>
              视频已上传
            </span>
            <a 
              href={videoUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ fontSize: 12, marginLeft: 'auto' }}
            >
              预览
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoUpload;
