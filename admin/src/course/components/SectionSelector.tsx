import React, { useState } from "react";
import {
  Card,
  Button,
  Form,
  Input,
  InputNumber,
  Switch,
  Space,
  List,
  Typography,
  Popconfirm,
  Modal,
  message
} from "antd";
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaArrowUp,
  FaArrowDown,
  FaVideo,
  FaClock,
  FaUnlock,
  FaLock
} from "react-icons/fa";
import { CourseSection } from "../schemas";
import VideoUpload from "./VideoUpload";
import { VIDEO_UPLOAD_CONFIG, COURSE_UPLOAD_PATHS } from "./constants";

const { Title, Text } = Typography;

interface SectionSelectorProps {
  value?: CourseSection[];
  onChange?: (sections: CourseSection[]) => void;
}

interface SectionFormData {
  title: string;
  duration: number;
  is_free: boolean;
  video_url?: string;
  is_published: boolean;
}

const SectionSelector: React.FC<SectionSelectorProps> = ({ value = [], onChange }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingSection, setEditingSection] = useState<CourseSection | null>(null);
  const [form] = Form.useForm<SectionFormData>();

  // 添加新章节
  const handleAddSection = () => {
    setEditingSection(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 编辑章节
  const handleEditSection = (section: CourseSection) => {
    setEditingSection(section);
    form.setFieldsValue({
      title: section.title,
      duration: section.duration,
      is_free: section.is_free,
      video_url: section.video_url,
      is_published: section.is_published,
    });
    setIsModalVisible(true);
  };

  // 删除章节
  const handleDeleteSection = (index: number) => {
    const newSections = value.filter((_, i) => i !== index);
    // 重新排序
    const reorderedSections = newSections.map((section, i) => ({
      ...section,
      sort_order: i + 1,
    }));
    onChange?.(reorderedSections);
    message.success('章节删除成功');
  };

  // 移动章节位置
  const handleMoveSection = (index: number, direction: 'up' | 'down') => {
    const newSections = [...value];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (targetIndex < 0 || targetIndex >= newSections.length) {
      return;
    }

    // 交换位置
    [newSections[index], newSections[targetIndex]] = [newSections[targetIndex], newSections[index]];
    
    // 重新排序
    const reorderedSections = newSections.map((section, i) => ({
      ...section,
      sort_order: i + 1,
    }));
    
    onChange?.(reorderedSections);
  };

  // 保存章节
  const handleSaveSection = async () => {
    try {
      const formData = await form.validateFields();
      
      if (editingSection) {
        // 编辑现有章节
        const newSections = value.map(section => 
          section.id === editingSection.id 
            ? { ...section, ...formData }
            : section
        );
        onChange?.(newSections);
        message.success('章节更新成功');
      } else {
        // 添加新章节
        const newSection: CourseSection = {
          id: Date.now(), // 临时ID，实际保存时由后端生成
          ...formData,
          sort_order: value.length + 1,
          course_id: 0, // 创建课程时会设置
          created_at: new Date(),
          updated_at: new Date(),
        };
        onChange?.([...value, newSection]);
        message.success('章节添加成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 格式化时长显示
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button 
          type="primary" 
          icon={<FaPlus />} 
          onClick={handleAddSection}
        >
          添加章节
        </Button>
      </div>

      <List
        dataSource={value}
        renderItem={(section, index) => (
          <List.Item>
            <Card 
              size="small" 
              style={{ width: '100%' }}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Text strong>第{section.sort_order}章</Text>
                  <Text>{section.title}</Text>
                  {section.is_free ? (
                    <FaUnlock style={{ color: '#52c41a' }} title="免费" />
                  ) : (
                    <FaLock style={{ color: '#faad14' }} title="付费" />
                  )}
                </div>
              }
              extra={
                <Space>
                  <Button 
                    size="small" 
                    icon={<FaArrowUp />} 
                    disabled={index === 0}
                    onClick={() => handleMoveSection(index, 'up')}
                  />
                  <Button 
                    size="small" 
                    icon={<FaArrowDown />} 
                    disabled={index === value.length - 1}
                    onClick={() => handleMoveSection(index, 'down')}
                  />
                  <Button 
                    size="small" 
                    icon={<FaEdit />} 
                    onClick={() => handleEditSection(section)}
                  />
                  <Popconfirm
                    title="确定要删除这个章节吗？"
                    onConfirm={() => handleDeleteSection(index)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button 
                      size="small" 
                      danger 
                      icon={<FaTrash />} 
                    />
                  </Popconfirm>
                </Space>
              }
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <Space>
                    <FaClock />
                    <Text type="secondary">{formatDuration(section.duration)}</Text>
                  </Space>
                  {section.video_url && (
                    <Space>
                      <FaVideo />
                      <Text type="secondary">有视频</Text>
                    </Space>
                  )}
                  <Text type={section.is_published ? 'success' : 'warning'}>
                    {section.is_published ? '已发布' : '未发布'}
                  </Text>
                </div>
              </Space>
            </Card>
          </List.Item>
        )}
        locale={{ emptyText: '暂无章节，点击上方按钮添加章节' }}
      />

      <Modal
        title={editingSection ? '编辑章节' : '添加章节'}
        open={isModalVisible}
        onOk={handleSaveSection}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText="保存"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            duration: 0,
            is_free: false,
            is_published: false,
          }}
        >
          <Form.Item
            name="title"
            label="章节标题"
            rules={[{ required: true, message: '请输入章节标题' }]}
          >
            <Input placeholder="请输入章节标题" />
          </Form.Item>

          <Form.Item
            name="duration"
            label="时长（秒）"
            rules={[{ required: true, message: '请输入章节时长' }]}
          >
            <InputNumber 
              min={0} 
              placeholder="请输入章节时长（秒）" 
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="video_url"
            label="视频URL"
          >
            <Input placeholder="请输入视频URL（可选）" />
          </Form.Item>

          <Form.Item
            name="is_free"
            label="是否免费"
            valuePropName="checked"
          >
            <Switch checkedChildren="免费" unCheckedChildren="付费" />
          </Form.Item>

          <Form.Item
            name="is_published"
            label="是否发布"
            valuePropName="checked"
          >
            <Switch checkedChildren="已发布" unCheckedChildren="未发布" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SectionSelector;
