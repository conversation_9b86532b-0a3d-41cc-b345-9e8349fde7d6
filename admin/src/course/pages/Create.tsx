import { Create, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  Select,
  message,
} from "antd";
import React, { useState } from "react";
import { entityKey } from "@/course";
import { useNavigation } from "@refinedev/core";
import { ImageUpload, SectionSelector } from "../components";
import { CourseSection } from "../schemas";
import { API_BASE_URL } from "@/common/env";

const { TextArea } = Input;

const CourseCreate: React.FC = () => {
  const { formProps, saveButtonProps, onFinish } = useForm({
    resource: entityKey,
  });

  const { goBack } = useNavigation();
  const [sections, setSections] = useState<CourseSection[]>([]);

  // 自定义保存逻辑
  const handleSave = async (values: any) => {
    try {
      // 1. 先保存课程基本信息（不包含sections）
      const courseData = {
        title: values.title,
        description: values.description,
        instructor: values.instructor,
        cover_image: values.cover_image,
        poster_url: values.poster_url,
        tags: values.tags || [],
      };

      // 调用原始的保存方法保存课程
      const courseResult = await onFinish(courseData);

      if (courseResult && sections.length > 0) {
        // 2. 如果有章节，分别保存每个章节
        const courseId = courseResult.data?.id;
        if (courseId) {
          for (const section of sections) {
            const sectionData = {
              title: section.title,
              duration: section.duration,
              sort_order: section.sort_order,
              is_free: section.is_free,
              video_url: section.video_url,
              is_published: section.is_published,
              course_id: courseId,
            };

            // 保存章节
            await fetch(`${API_BASE_URL}/entity/courseSection`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(sectionData),
            });
          }
          message.success('课程和章节保存成功！');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请重试');
    }
  };

  return (
    <Create
      saveButtonProps={saveButtonProps}
      headerButtons={[
        <button key="cancel" onClick={() => goBack()} className="ant-btn">
          取消
        </button>,
      ]}
    >
      <Form<any> {...formProps} layout="vertical" onFinish={handleSave}>
        <Form.Item
          label="课程标题"
          name="title"
          rules={[{ required: true, message: "请输入课程标题" }]}
        >
          <Input placeholder="请输入课程标题" />
        </Form.Item>

        <Form.Item
          label="课程描述"
          name="description"
          rules={[{ required: true, message: "请输入课程描述" }]}
        >
          <TextArea 
            rows={4} 
            placeholder="请输入课程描述" 
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item
          label="导师"
          name="instructor"
        >
          <Input placeholder="请输入导师名称" />
        </Form.Item>

        <Form.Item
          label="课程封面"
          name="cover_image"
        >
          <ImageUpload
            uploadPath="courses/covers/"
            buttonText="上传课程封面"
            showPreview={false}
          />
        </Form.Item>

        <Form.Item
          label="海报图片"
          name="poster_url"
        >
          <ImageUpload
            uploadPath="courses/posters/"
            buttonText="上传海报图片"
            showPreview={false}
          />
        </Form.Item>

        <div style={{ marginBottom: 24 }}>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
            课程章节
          </label>
          <SectionSelector
            value={sections}
            onChange={setSections}
          />
        </div>

        <Form.Item
          label="标签"
          name="tags"
        >
          <Select
            mode="tags"
            placeholder="请输入标签，按回车添加"
            style={{ width: '100%' }}
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Form>
    </Create>
  );
};

export default CourseCreate;
