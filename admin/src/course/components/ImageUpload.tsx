import React, { useState, useEffect } from "react";
import { Upload, Button, message } from "antd";
import type { UploadProps } from "antd";
import { API_BASE_URL } from "@/common/env";
import { FaUpload } from "react-icons/fa";

interface ImageUploadProps {
  value?: string;
  onChange?: (url: string) => void;
  uploadPath: string;
  buttonText: string;
  previewWidth?: number;
  previewHeight?: number;
  showPreview?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  uploadPath,
  buttonText,
  previewWidth = 200,
  previewHeight = 120,
  showPreview = true,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(value);

  // 当外部value变化时，更新预览URL
  useEffect(() => {
    setPreviewUrl(value);
  }, [value]);

  const uploadProps: UploadProps = {
    name: 'file',
    action: `${API_BASE_URL}/files/upload_to_minio`,
    data: {
      path: uploadPath,
    },
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        const uploadedUrl = info.file.response.data.url;
        // 立即更新预览URL
        setPreviewUrl(uploadedUrl);
        // 调用外部传入的onChange回调
        onChange?.(uploadedUrl);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <div>
      <Upload
        {...uploadProps}
        showUploadList={false}
      >
        <Button icon={<FaUpload />}>{buttonText}</Button>
      </Upload>
      {showPreview && previewUrl && (
        <div className="mt-2">
          <img
            src={previewUrl}
            alt="图片预览"
            style={{
              width: previewWidth,
              height: previewHeight,
              borderRadius: '8px',
              objectFit: 'cover'
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
