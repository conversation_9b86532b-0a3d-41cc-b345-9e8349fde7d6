// 图片上传相关常量配置

/**
 * 支持的图片文件类型
 */
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp'
] as const;

/**
 * 图片文件最大大小（MB）
 */
export const MAX_IMAGE_SIZE = 5;

/**
 * 图片上传配置
 */
export const IMAGE_UPLOAD_CONFIG = {
  maxSize: MAX_IMAGE_SIZE,
  acceptedTypes: SUPPORTED_IMAGE_TYPES,
  helpText: `支持 ${SUPPORTED_IMAGE_TYPES.map(type => type.split('/')[1].toUpperCase()).join('、')} 格式，文件大小不超过 ${MAX_IMAGE_SIZE}MB`
} as const;

/**
 * 课程相关图片上传路径
 */
export const COURSE_IMAGE_PATHS = {
  cover: 'courses/covers/',
  poster: 'courses/posters/'
} as const;
