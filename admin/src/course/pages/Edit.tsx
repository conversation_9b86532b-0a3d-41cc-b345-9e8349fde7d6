import { Edit, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  Select,
} from "antd";
import React from "react";
import { entityKey } from "@/course";
import { useNavigation } from "@refinedev/core";
import { ImageUpload, SectionSelector } from "../components";
import { CourseSection } from "../schemas";

const { TextArea } = Input;

const CourseEdit: React.FC = () => {
  const { formProps, saveButtonProps } = useForm({
    resource: entityKey,
  });

  const { goBack } = useNavigation();

  return (
    <Edit
      saveButtonProps={saveButtonProps}
      headerButtons={[
        <button key="cancel" onClick={() => goBack()} className="ant-btn">
          取消
        </button>,
      ]}
    >
      <Form<any> {...formProps} layout="vertical">
        <Form.Item
          label="课程标题"
          name="title"
          rules={[{ required: true, message: "请输入课程标题" }]}
        >
          <Input placeholder="请输入课程标题" />
        </Form.Item>

        <Form.Item
          label="课程描述"
          name="description"
          rules={[{ required: true, message: "请输入课程描述" }]}
        >
          <TextArea 
            rows={4} 
            placeholder="请输入课程描述" 
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item
          label="导师"
          name="instructor"
        >
          <Input placeholder="请输入导师名称" />
        </Form.Item>

        <Form.Item
          label="课程封面"
          name="cover_image"
        >
          <ImageUpload
            uploadPath="courses/covers/"
            buttonText="上传课程封面"
            showPreview={true}
          />
        </Form.Item>

        <Form.Item
          label="海报图片"
          name="poster_url"
        >
          <ImageUpload
            uploadPath="courses/posters/"
            buttonText="上传海报图片"
            showPreview={true}
          />
        </Form.Item>

        <Form.Item
          label="课程章节"
          name="sections"
        >
          <SectionSelector />
        </Form.Item>

        <Form.Item
          label="标签"
          name="tags"
        >
          <Select
            mode="tags"
            placeholder="请输入标签，按回车添加"
            style={{ width: '100%' }}
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Form>
    </Edit>
  );
};

export default CourseEdit;
